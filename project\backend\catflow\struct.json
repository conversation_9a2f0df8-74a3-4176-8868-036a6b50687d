{"meta": {"description": "旅行规划智能体结构配置文件", "purpose": "定义旅行规划系统的多智能体协作结构", "structure": {"structures": {"type": "object", "description": "结构配置对象，包含所有智能体结构的定义", "properties": {"[structure_name]": {"type": "object", "description": "单个智能体结构的配置", "properties": {"name": {"type": "string", "description": "结构名称，用于标识该结构", "required": true}, "description": {"type": "string", "description": "结构描述，说明该结构的用途和特点", "required": false}, "type": {"type": "string", "description": "结构类型，如coordinator、sequential、concurrent、loop等", "required": false, "enum": ["coordinator", "sequential", "concurrent", "loop"]}, "global_instruction": {"type": "string", "description": "全局指令，适用于整个结构的通用指导", "required": false}, "generate_content_config": {"type": "object", "description": "内容生成配置", "properties": {"temperature": {"type": "number", "description": "生成温度，控制输出的随机性", "range": "0.0-1.0"}, "max_output_tokens": {"type": "integer", "description": "最大输出token数量"}}}, "main_agent": {"type": "object", "description": "主智能体配置，每个结构必须有且仅有一个主智能体", "required": true, "properties": {"name": {"type": "string", "description": "主智能体名称，通常为main_agent"}, "type": {"type": "string", "description": "主智能体类型", "enum": ["sequential_agent", "concurrent_agent", "loop_agent"]}, "model": {"type": "string", "description": "使用的语言模型，需在llm_config.json中定义"}, "max_iterations": {"type": "integer", "description": "最大迭代次数（仅适用于loop_agent类型）", "required": false}, "instruction": {"type": "string", "description": "主智能体的详细指令"}, "description": {"type": "string", "description": "主智能体的描述信息"}, "sub_structures": {"type": "array", "description": "主智能体包含的子结构列表，用于构建复合智能体结构", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "子结构名称"}, "type": {"type": "string", "description": "子结构类型", "enum": ["sequential", "concurrent", "loop"]}, "description": {"type": "string", "description": "子结构描述"}, "agent_refs": {"type": "array", "description": "子结构中引用的智能体列表，需在agent_config.json中定义", "items": {"type": "string"}}, "max_iterations": {"type": "integer", "description": "最大迭代次数（仅适用于loop类型）"}}}, "required": false}}}}}}}}, "usage": {"description": "使用方法", "steps": ["1. 在structures对象中定义新的智能体结构", "2. 设置结构的基本属性：name、description", "3. 配置main_agent定义主智能体", "4. 在sub_structures中定义子结构", "5. 设置全局指令和生成配置", "6. 确保所有agent_refs中的智能体都在agent_config.json中定义"], "structure_types": {"coordinator": "协调器类型，用于管理和协调多个智能体", "sequential": "顺序执行类型，智能体按顺序依次执行", "concurrent": "并发执行类型，智能体同时并行执行", "loop": "循环执行类型，智能体循环执行直到满足退出条件"}, "agent_types": {"loop_agent": "循环智能体，可以进行多次迭代处理", "sequential_agent": "顺序智能体，按顺序执行子结构", "concurrent_agent": "并发智能体，并行执行子结构"}, "best_practices": ["合理设计智能体的分工和协作关系", "为循环类型设置合适的max_iterations避免无限循环", "使用描述性的名称和详细的description", "确保sub_structures中的智能体引用都在agent_config.json中定义", "根据任务特点选择合适的结构类型和智能体类型", "设置合适的temperature和max_output_tokens参数", "编写清晰的instruction指导智能体行为", "使用global_instruction设置整体的行为准则"]}}, "structures": {"travel_planning_structure": {"name": "旅行规划结构", "description": "用于旅行规划的多智能体协作结构", "type": "coordinator", "global_instruction": "请以专业、友好的语调回复用户，提供详细且实用的旅行建议。所有回复都应该结构化、清晰，并包含具体的建议和选项。", "generate_content_config": {"temperature": 0.7, "max_output_tokens": 204800}, "main_agent": {"name": "main_agent", "type": "loop_agent", "model": "qwen-max", "max_iterations": 1, "instruction": "你是一个旅行计划协调器，负责生成完整的旅行计划报告。你需要协调多个专门的子结构来收集信息并生成最终报告。必须按以下顺序执行所有步骤：\n1. 首先转移到plan_agent获取旅行计划\n2. 然后转移到sequential_agent中获取天气、餐厅和酒店信息\n3. 最后转移到summary_agent里生成最终报告。\n确保完成所有三个步骤后生成最终报告。", "description": "主agent定义，每个结构下必须要有这个agent，且只能有一个,且这个agent不可从agent_manager.py中获取，它是LlmAgent类型，model:参数代表模型名称，只从llm_manager.py中获取，不另写代码", "sub_structures": [{"name": "plan_agent", "type": "sequential", "description": "包含旅行计划智能体的顺序结构", "agent_refs": ["planTravelAgent"]}, {"name": "sequential_agent", "type": "sequential", "description": "包含天气、餐厅、酒店智能体的顺序结构", "agent_refs": ["weatherAgent", "restaurantAgent", "hotelAgent"]}, {"name": "summary_agent", "type": "sequential", "description": "生成最终旅行计划报告的智能体结构", "agent_refs": ["contextAgent", "summaryAgent"]}]}}}, "market_research_structure": {"name": "市场调研结构", "description": "用于全流程市场调研的多智能体协作结构，融合顺序执行、并发处理和循环验证机制", "type": "coordinator", "global_instruction": "以客观、严谨的态度开展市场调研，确保数据真实可靠、分析深入全面。所有输出需包含具体数据支撑，并提供可落地的商业建议。", "generate_content_config": {"temperature": 0.4, "max_output_tokens": 204800}, "main_agent": {"name": "research_coordinator", "type": "sequential_agent", "model": "qwen-max", "instruction": "作为市场调研总协调者，需按以下流程执行：1. 启动数据采集阶段（并发执行多渠道数据收集）；2. 执行数据清洗与标准化（顺序处理）；3. 开展循环验证（确保核心数据准确性）；4. 完成综合分析与报告生成。严格把控各阶段衔接，确保调研质量。", "description": "市场调研总协调智能体，负责统筹全流程子结构协作", "sub_structures": [{"name": "data_collection_phase", "type": "concurrent", "description": "多渠道数据并发采集阶段，同时从不同来源获取市场信息", "agent_refs": ["onlineSurveyAgent", "retailAuditAgent", "socialListeningAgent", "industryReportAgent"]}, {"name": "data_processing_phase", "type": "sequential", "description": "数据处理流水线，按顺序执行清洗、转换和标准化", "agent_refs": ["dataCleaningAgent", "formatConversionAgent", "normalizationAgent", "duplicateRemovalAgent"]}, {"name": "data_validation_loop", "type": "loop", "description": "循环验证机制，反复校验核心数据直至达标", "max_iterations": 5, "agent_refs": ["accuracyCheckAgent", "sampleVerificationAgent", "crossSourceAgent"]}, {"name": "comprehensive_analysis", "type": "sequential", "description": "综合分析阶段，先拆分维度分析再进行整合", "sub_structures": [{"name": "dimension_analysis", "type": "concurrent", "description": "多维度并行分析", "agent_refs": ["competitorAnalysisAgent", "consumerInsightAgent", "trendForecastAgent", "pricingStrategyAgent"]}, {"name": "report_synthesis", "type": "sequential", "description": "报告合成与优化", "agent_refs": ["insightIntegrationAgent", "visualizationAgent", "recommendationAgent", "reportPolishAgent"]}]}]}}}